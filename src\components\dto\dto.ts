import { ExamType } from '../../Config/Contanst';
import { ReviewData } from './../../mock/shopData';
import { ImageOrVideo } from "react-native-image-crop-picker";

export interface OriginDto {
    id: string;
    Origin: string;
}

export interface LabelProductDto {
    label: string;
    id: string;
    icon: string
}

export interface ListProductCreateChildDto {
    id: string;
    title: string;
    icon: string
}

export interface MenuShopProps {
    select: string
    setSelect: (select: string) => void
}


export interface RegisterShop {
    id?: number
    StoreName: string
    StorePhone: string
    Email: string
    BusinessType: string
    desscription: string
    Address?: string
}

export interface LeaderShopInfoData {
    Id?: string
    DateCreated?: number
    name: string
    Phone: string
    Email: string
    // BusinessType: string
    description: string,
    Status: number,
    Address?: string
}

export interface StoreInfoFormProps {
    statusInput: string,
    data: LeaderShopInfoData
}
export interface DescriptionProps {
    image: ImageOrVideo[]
    imageListEdit: ImageOrVideo[]
    pickerImg: any
    avataProduct: string
}

export interface ListProducts {
    name: string,
    number: number,
}

export interface DetailProductByType {
    id?: number,
    image: string[],
    ProductName: string,
    star: number,
    price: string,
    status: string
    inStock: number,
    like: number,
    shopId: string,
    disaple: boolean
}

export interface ReviewDataDto {
    id?: string,
    avatar: string,
    UserName: string,
    rating: string,
    description: string,
    imagesProduct: string[]
    shopId: string
    product: {
        productImage: string
        productName: string,
        property: string
    }

}

export interface ReviewOrderDataDto {
    id?: string,
    avatar: string,
    UserName: string,
    rating: string,
    description: string,
    imagesProduct: string[]
    shopId: string
    Order: {
        orderId: string,
    }
}

export interface menuOrder {
    id: String,
    name: string,
    numberOrder: number,
}

export interface User {
    avatarUrl: string;
    name: string;
    rank: string;
    status: string,
    rankUrl: string
}

export interface ReviewProductProps {
    type: String;
}

export interface ListItemProps {
    dataProduct: any[]
    setSelecChildID: (item: any) => void;
    setSelecChildName: (item: any) => void;
}

export interface ManageProductDetailProps {
    menu: string;
    dataShop: any[]
}