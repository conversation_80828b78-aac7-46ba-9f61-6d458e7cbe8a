import { useNavigation } from '@react-navigation/native';
import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { TextField, Winicon } from 'wini-mobile-components';
import { RootScreen } from '../../router/router';
import { Title } from '../../Config/Contanst';
import { ScrollView } from 'react-native-gesture-handler';
import { Controller, useForm } from 'react-hook-form';
import { TextInput } from 'react-native-paper';
import { LeaderShopInfoData } from '../dto/dto';


interface LeaderShopInfoProps {
    shop: any[]
}

const LeaderShopInfo = (props: LeaderShopInfoProps) => {
    const { shop } = props
    const navigation = useNavigation<any>();
    const [stateEdit, setStateEdit] = useState<LeaderShopInfoData>({
        Id: "",
        DateCreated: 0,
        name: "",
        Phone: "",
        Email: "",
        description: "",
        Status: 0,
        Address: ""
    })
    const handleNavigateOrders = (order: string, dataEdit: LeaderShopInfoData, status?: string) => {
        navigation.push(order, { data: dataEdit, status: status });
    }
    useEffect(() => {
        if (shop && shop.length > 0) {
            setStateEdit({
                name: shop[0]?.Name,
                Phone: shop[0]?.Mobile,
                Email: shop[0]?.Email,
                description: shop[0]?.Description,
                Status: shop[0]?.Status,
                Id: shop[0]?.Id,
                DateCreated: shop[0]?.DateCreated,
                Address: shop[0]?.Address
            })
        }
    }, [shop])
    return (
        <View style={styles.container}>
            <ScrollView>
                <View style={styles.card}>
                    <View style={styles.cardHeader}>
                        <View style={styles.cardHeaderLeft}>
                            <Winicon src="fill/users/users" size={20} color={'orange'} />
                            <Text style={styles.cardTitle}>Thông tin chi tiết cửa hàng</Text>
                        </View>
                        <TouchableOpacity style={styles.cardHeaderRight} onPress={() => handleNavigateOrders(RootScreen.RegisterShop, stateEdit, "edit")}>
                            <Winicon src="fill/text/file-edit" size={20} color={'orange'} />
                        </TouchableOpacity>
                    </View>
                    <View style={styles.cardContent}>
                        <Text style={styles.infoValue}>Họ và tên: <Text style={styles.infoValue}>{stateEdit.name}</Text></Text>
                        <Text style={styles.infoValue}>SDT: <Text style={styles.infoValue}>{stateEdit.Phone}</Text></Text>
                        <Text style={styles.infoValue}>Email: <Text style={styles.infoValue}>{stateEdit.Email}</Text></Text>
                        {/* <Text style={styles.infoValue}>Ngành nghề: <Text style={styles.infoValue}>{stateEdit.BusinessType}</Text></Text> */}
                        <Text style={styles.infoValue}>Mô tả: <Text style={styles.infoValue}>{stateEdit.description}</Text></Text>
                        <Text style={styles.infoValue}>Trạng thái: <Text style={styles.infoValue}>{stateEdit.Status == 0 ? "Khởi tạo" : "Hoạt động"}</Text></Text>
                    </View>
                </View>
            </ScrollView>

        </View >
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#fff',
        marginTop: 10
    },
    navBar: {
        flexDirection: 'row',
        justifyContent: 'space-around',
        paddingVertical: 10,
        backgroundColor: '#fff',
    },
    navItem: {
        alignItems: 'center',
        borderColor: "#00FFFF",
        borderWidth: 0.3,
        padding: 10,
        borderRadius: 10,
        maxWidth: 67,
        maxHeight: 68

    },
    navText: {
        fontSize: 10,
        color: '#333',
        marginTop: 4,
        textAlign: 'center',
    },
    navBadge: {
        position: 'absolute',
        top: 5,
        right: 4,
        backgroundColor: '#FF0000',
        color: '#fff',
        fontSize: 10,
        paddingHorizontal: 4,
        borderRadius: 10,
    },
    card: {
        backgroundColor: '#fff',
        borderRadius: 8,
        margin: 10,
        padding: 15,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 2,
    },
    cardHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 10,
    },
    cardTitle: {
        fontSize: 20,
        fontWeight: 'bold',
        color: '#333',
        textAlign: 'center',
    },
    cardHeaderLeft: {
        display: "flex",
        flexDirection: "row",
        alignContent: "center",
        gap: 3,
        flex: 1
    },
    cardHeaderRight: {
    },

    cardContent: {
        marginTop: 10,
    },
    infoLabel: {
        fontSize: 14,
        color: '#666',
        marginBottom: 5,
    },
    infoValue: {
        fontSize: 18,
        color: '#000',
        fontWeight: '500',
    },
    infoValueEdit: {
        lineHeight: 18,
        display: "flex",
        alignItems: "center",
        color: '#000',
        fontWeight: '500',

    },

});

export default LeaderShopInfo;