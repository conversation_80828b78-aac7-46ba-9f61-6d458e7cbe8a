/* eslint-disable react-native/no-inline-styles */
import React, { useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
    Animated,
    Dimensions,
    Linking,
    RefreshControl,
    StyleSheet,
    TouchableOpacity,
    View,
    Image,
    Text

} from 'react-native';
import { AppButton, FBottomSheet, showBottomSheet, Winicon } from 'wini-mobile-components';


const HeaderShop = () => {



    return (
        <View style={styles.header}>
            {/* Header */}
            <Image
                source={require('../../assets/images/header_group.png')}
                style={styles.headerImage}
            />
        </View >
    );
};

const styles = StyleSheet.create({
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        maxHeight: 120
    },
    headerImage: {

    },
    headerTitle: {
        fontSize: 20,
        fontWeight: 'bold',
        color: '#fff',
    },

});


export default HeaderShop
