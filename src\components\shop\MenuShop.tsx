/* eslint-disable react-native/no-inline-styles */
import React, { useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
    Animated,
    Dimensions,
    Linking,
    RefreshControl,
    StyleSheet,
    TouchableOpacity,
    View,
    Image,
    Text

} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { RootScreen } from '../../router/router';
import LinearGradient from 'react-native-linear-gradient';
import { Winicon } from 'wini-mobile-components';
import { TypeMenuShop } from '../../Config/Contanst';
import { MenuShopProps } from '../dto/dto';




const MenuShop = (props: MenuShopProps) => {
    const { select, setSelect } = props
    return (
        <View style={styles.tabs}>
            {select == TypeMenuShop.User ?
                <View>
                    <LinearGradient start={{ x: 0, y: 0 }} end={{ x: 1, y: 0 }} colors={['#90C8FB', '#8DC4F7', '#B6F5FE']} style={styles.linearGradient}>
                        <Winicon src="fill/location/explore-user" size={20} color="#1C33FF" />
                        <Text style={styles.tabTextAction}>Cá nhân</Text>
                    </LinearGradient>
                </View>
                :
                <TouchableOpacity style={styles.tab} onPress={() => setSelect(TypeMenuShop.User)}>
                    <View style={styles.tabWrapper}>
                        <Winicon src="fill/location/explore-user" size={20} color="#262626" />
                        <Text style={styles.tabText}>Cá nhân</Text>
                    </View>
                </TouchableOpacity>
            }

            {select == TypeMenuShop.Afiliate ?
                <View>
                    <LinearGradient start={{ x: 0, y: 0 }} end={{ x: 1, y: 0 }} colors={['#90C8FB', '#8DC4F7', '#B6F5FE']} style={styles.linearGradient}>
                        <Winicon src="color/business/currency-dollar" size={20} color="#1C33FF" />
                        <Text style={styles.tabTextAction}>Affiliate</Text>
                    </LinearGradient>
                </View>
                :
                <TouchableOpacity style={styles.tab} onPress={() => setSelect(TypeMenuShop.Afiliate)}>
                    <View style={styles.tabWrapper}>
                        <Winicon src="color/business/currency-dollar" size={20} color="#262626" />
                        <Text style={styles.tabText}>Affiliate</Text>
                    </View>
                </TouchableOpacity>
            }

            {select == TypeMenuShop.Shop ?
                <View >
                    <LinearGradient start={{ x: 0, y: 0 }} end={{ x: 1, y: 0 }} colors={['#90C8FB', '#8DC4F7', '#B6F5FE']} style={styles.linearGradient}>
                        <Winicon src="fill/shopping/store" size={21} color="#1C33FF" />
                        <Text style={[styles.tabTextAction]}>Shop</Text>
                    </LinearGradient>
                </View>
                :
                <TouchableOpacity onPress={() => setSelect(TypeMenuShop.Shop)}>
                    <View style={styles.tabWrapper}>
                        <Winicon src="fill/shopping/store" size={21} color="#262626" />
                        <Text style={[styles.tabText]}>Shop</Text>
                    </View>
                </TouchableOpacity>

            }
            {select == TypeMenuShop.Wallet ?
                <View >
                    <LinearGradient start={{ x: 0, y: 0 }} end={{ x: 1, y: 0 }} colors={['#90C8FB', '#8DC4F7', '#B6F5FE']} style={styles.linearGradient}>
                        <Winicon src="fill/shopping/wallet-44" size={21} color="#1C33FF" />
                        <Text style={styles.tabTextAction}>Ví</Text>
                    </LinearGradient>
                </View>
                :
                <TouchableOpacity style={styles.tab} onPress={() => setSelect(TypeMenuShop.Wallet)}>
                    <View style={styles.tabWrapper}>
                        <Winicon src="fill/shopping/wallet-44" size={21} color="#262626" />
                        <Text style={styles.tabText}>Ví</Text>
                    </View>
                </TouchableOpacity>
            }
        </View>
    );
};

const styles = StyleSheet.create({
    tabs: {
        flexDirection: 'row',
        justifyContent: 'space-evenly',
        alignItems: 'center',
        marginTop: 20,
    },
    tab: {
    },
    tabWrapper: {
        flexDirection: "row",
        alignItems: "center",
        padding: 4
    },
    linearGradient: {
        flexDirection: "row",
        alignItems: "center",
        backgroundColor: "#90C8Fb",
        padding: 4,
        borderRadius: 10
    },
    tabText: {
        fontSize: 16,
        color: '#262626',
        marginLeft: 5
    },
    tabTextAction: {
        fontSize: 16,
        color: '#1C33FF',
        marginLeft: 5
    },
    activeTab: {
        borderBottomWidth: 2,
        borderBottomColor: '#2196F3',
    },
    activeTabText: {
        color: '#2196F3',
        fontWeight: 'bold',
    },
    ImageIcon: {
        width: 22,
        height: 22,
    }

});


export default MenuShop
