import React, { use, useEffect, useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Pressable } from 'react-native';
import { FlatList } from 'react-native-gesture-handler';
import { ListTile, Winicon } from 'wini-mobile-components';
import { useNavigation } from '@react-navigation/native';
import ListItemCard from '../card/CartProductCreate';
import { DataController } from '../../../base/baseController';
import { ListItemProps } from '../../dto/dto';


const ListItem = (props: ListItemProps) => {
    const { dataProduct, setSelecChildID, setSelecChildName } = props
    const [isSelected, setIsSelected] = useState("");
    const [data, setData] = useState<any[]>();
    useEffect(() => {
        if (dataProduct && dataProduct?.length > 0) {
            setData(dataProduct)
        }
    }, [dataProduct])
    useEffect(() => {
        console.log("check-isSelected", isSelected)
    }, [isSelected])

    const handleSelect = (item: any) => {
        setSelecChildID(item?.Id)
        setSelecChildName(item?.Name)
        setIsSelected(item.Id)
    }

    return (
        <FlatList
            data={data}
            style={{ flex: 1 , marginBottom : 50 }}
            keyExtractor={(item, i) => `${i} ${item.id}`}
            renderItem={({ item, index }) => ListItemCard({ item, index }, isSelected, handleSelect)}
        />
    );
};

const styles = StyleSheet.create({
    itemContainer: {
        flexDirection: 'row',
        justifyContent: 'space-around',
        alignItems: 'center',
        padding: 10,
        borderBottomWidth: 1,
        borderBottomColor: '#eee',
    },
    itemText: {
        fontSize: 20,
        color: 'black',
        flex: 1,
    },
    radio: {
        width: 30,
        height: 30,
        borderRadius: 50,
        borderWidth: 2,
        borderColor: '#007AFF',
    },
    radioSelected: {
        backgroundColor: '#007AFF',
        padding: 5
    },
});

export default ListItem;