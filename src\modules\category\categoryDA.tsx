import { DataController } from "../../base/baseController";

class categoryDA {
    private categoryController: DataController;
  constructor() {
    this.categoryController = new DataController('Category');
  }
  async getAll() {
    const response = await this.categoryController.getListSimple(
        {
          query: '*',
          returns: ['Id', 'Name', 'Img'],
          sortby: {BY: 'Sort', DIRECTION: 'ASC'},
        },
    );
    if (response?.code === 200) {
      return response;
    }
    return null;
  }
}

export default categoryDA;
