import { StyleSheet, View, Text, TouchableOpacity, ScrollView, TextInput } from "react-native";
import React, { useEffect, useState } from "react";
import HeaderShop from "../../components/shop/HeaderShop";
import NavigateShop from "../../components/shop/NavigateShop";
import { TextField, Winicon } from 'wini-mobile-components';
import { DataController } from "../../base/baseController";
import { LoadingUI } from "../../features/loading";
import { useSelectorShopState } from "../../redux/hook/shopHook ";

// Tab types for affiliate config
enum AffiliateTabType {
    DEFAULT = "DEFAULT",
    SHOP = "SHOP",
    CATEGORY = "CATEGORY"
}

const ConfigAffiliate = () => {
    const [activeTab, setActiveTab] = useState<AffiliateTabType>(AffiliateTabType.DEFAULT);
    const [shopSettings, setShopSettings] = useState({
        customerPurchase: '5',
        f1Reward: '10',
        f2Reward: '5',
        isEnabled: true
    });
    const [defaultReward, setDefaultReward] = useState<Array<any>>();
    const [ShopReward, setShopReward] = useState<Array<any>>();
    const controller = new DataController('Reward');
    const [loading, setLoading] = useState(true);
    useEffect(() => {
        fetchDefaultReward();
    }, []);
    const fetchDefaultReward = async () => {
        const res = await controller.getAll();
        if (res.code === 200) {
            setDefaultReward(res.data);
        }
        setLoading(false);
    };
    // Edit mode state for each row
   
    const handleShopSettingsChange = (field: string, value: string | boolean) => {
        setShopSettings(prev => ({
            ...prev,
            [field]: value
        }));
    };

    const handleTempValueChange = (field: string, value: string) => {
        setTempValues(prev => ({
            ...prev,
            [field]: value
        }));
    };

    const handleEditPress = (field: string) => {
        setTempValues(prev => ({
            ...prev,
            [field]: shopSettings[field as keyof typeof shopSettings] as string
        }));
        setEditModes(prev => ({
            ...prev,
            [field]: true
        }));
    };

    const handleSavePress = (field: string) => {
        setShopSettings(prev => ({
            ...prev,
            [field]: tempValues[field as keyof typeof tempValues]
        }));
        setEditModes(prev => ({
            ...prev,
            [field]: false
        }));
    };

    const handleCancelPress = (field: string) => {
        setEditModes(prev => ({
            ...prev,
            [field]: false
        }));
        setTempValues(prev => ({
            ...prev,
            [field]: shopSettings[field as keyof typeof shopSettings] as string
        }));
    };

    // Commission settings state
    const [commissionSettings, setCommissionSettings] = useState({
        customerPurchase: '5',
        f1Reward: '10',
        f2Reward: '5'
    });

    // Tab configuration
    const tabs = [
        {
            id: AffiliateTabType.DEFAULT,
            name: 'Mặc định'
        },
        {
            id: AffiliateTabType.SHOP,
            name: 'SHOP'
        },
        {
            id: AffiliateTabType.CATEGORY,
            name: 'Danh mục'
        }
    ];

    const renderTabContent = () => {
        switch (activeTab) {
            case AffiliateTabType.DEFAULT:
                return renderDefaultSettings();
            case AffiliateTabType.SHOP:
                return renderShopSettings();
            case AffiliateTabType.CATEGORY:
                return renderCategorySettings();
            default:
                return renderDefaultSettings();
        }
    };

    const handleCommissionChange = (field: string, value: string) => {
        setCommissionSettings(prev => ({
            ...prev,
            [field]: value
        }));
    };

    const renderDefaultSettings = async () => {
        return (
            loading ? (
                <LoadingUI isLoading={loading} />
            ) :
                <ScrollView style={styles.tabContent}>
                    {
                        defaultReward?.map((item: any) => (
                            <View style={styles.commissionItem}>
                                <Text style={styles.commissionLabel}>{item.Name}</Text>
                                <View style={styles.percentageContainer}>
                                    <Text
                                        style={styles.percentageInput}
                                    >
                                        {item.Percent}%
                                    </Text>
                                </View>
                            </View>
                        ))
                    }
                </ScrollView>);
    };

    const renderCommissionRow = (item: any) => (
        <View style={styles.commissionItem}>
            <Text style={styles.commissionLabel}>{item.Name}</Text>
            <View style={{ ...styles.percentageContainer, backgroundColor: '#fff', width: 150 }}>
                {item.isEdit ? (
                    <TextInput
                        value={`${item.Percent}`}
                        onChangeText={(value: string) => handleTempValueChange(field, value)}
                        style={styles.percentageInputEdit}
                        keyboardType="numeric"
                        autoFocus
                    />
                ) : (
                    <Text style={styles.percentageInput}>
                        {item.Percent}%
                    </Text>
                )}
            </View>
            <View style={styles.editActionsContainer}>
                {item.isEdit ? (
                    <>
                        <TouchableOpacity
                            style={styles.saveButton}
                            onPress={() => handleSavePress(field)}
                        >
                            <Text style={styles.saveButtonText}>✓</Text>
                        </TouchableOpacity>
                        <TouchableOpacity
                            style={styles.cancelButton}
                            onPress={() => handleCancelPress(field)}
                        >
                            <Text style={styles.cancelButtonText}>✕</Text>
                        </TouchableOpacity>
                    </>
                ) : (
                    <TouchableOpacity
                        style={styles.editButton}
                        onPress={() => handleEditPress(field)}
                    >
                        <Winicon src="fill/user interface/i-edit" size={15} />
                    </TouchableOpacity>
                )}
            </View>
        </View>
    );

    const renderShopSettings = () => {
        const fetchData = async () => {
            const controllerShop = new DataController('ShopReward');
            const shopInfo = useSelectorShopState().data;
            setLoading(true);
            const res = await controllerShop.getListSimple(
                {
                    page: 1,
                    size: 10,
                    query: `@ShopId: {${shopInfo?.Id}} @Status: {1}`,
                    returns: ['Id', 'Name', 'Percent'],
                    sortby: { BY: 'DateCreated', DIRECTION: 'DESC' },
                }
            );
            if (res.code === 200 && res.data.length > 0) {
                setShopReward(res.data);
            }else{
                setShopReward(defaultReward);
            }
            setLoading(false);
        };
        useEffect(() => {
            fetchData();
        }, []);
        return (
        <ScrollView style={styles.tabContent}>
            {
                loading ? (
                    <LoadingUI isLoading={loading} />
                ) : defaultReward?.map((item: any) => (
                    renderCommissionRow(item)
                ))
            }
            {/* Toggle Switch */}
            <View style={styles.toggleContainer}>
                <Text style={styles.toggleLabel}>ON</Text>
                <TouchableOpacity
                    style={[styles.toggleSwitch, shopSettings.isEnabled && styles.toggleSwitchActive]}
                    onPress={() => handleShopSettingsChange('isEnabled', !shopSettings.isEnabled)}
                >
                    <View style={[
                        styles.toggleThumb,
                        shopSettings.isEnabled && styles.toggleThumbActive
                    ]} />
                </TouchableOpacity>
            </View>
        </ScrollView>);
    };

    const renderCategorySettings = () => (
        <ScrollView style={styles.tabContent}>
            <View style={styles.emptyState}>
                <Text style={styles.emptyText}>Cấu hình Danh mục đang được phát triển</Text>
            </View>
        </ScrollView>
    );

    return (
        <View style={styles.container}>
            {/* Header */}
            <View style={styles.header}>
                <HeaderShop />
            </View>
            <NavigateShop
                title={'Cấu hình affiliate'}
            />

            {/* Tab Navigation */}
            <View style={styles.tabContainer}>
                {tabs.map((tab) => (
                    <TouchableOpacity
                        key={tab.id}
                        style={styles.tab}
                        onPress={() => setActiveTab(tab.id)}
                    >
                        <Text style={[
                            styles.tabText,
                            activeTab === tab.id && styles.activeTabText
                        ]}>
                            {tab.name}
                        </Text>
                        {activeTab === tab.id && <View style={styles.activeTabIndicator} />}
                    </TouchableOpacity>
                ))}
            </View>

            {/* Tab Content */}
            <View style={styles.content}>
                {renderTabContent()}
            </View>
        </View >
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#fff',
    },
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        maxHeight: 120
    },
    navigator: {
        display: "flex",
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "space-between",
        borderBottomColor: "#00FFFF",
        paddingBottom: 18,
        borderBottomWidth: 0.5,
    },
    content: {
        flex: 1,
        backgroundColor: '#f5f5f5',
    },
    // Tab Navigation Styles
    tabContainer: {
        flexDirection: 'row',
        backgroundColor: '#fff',
        paddingHorizontal: 16,
        paddingTop: 16,
        paddingBottom: 0,
    },
    tab: {
        flex: 1,
        alignItems: 'center',
        paddingVertical: 12,
        paddingBottom: 16,
        position: 'relative',
    },
    tabText: {
        fontSize: 14,
        color: '#999',
        fontWeight: '400',
    },
    activeTabText: {
        color: '#00BFFF',
        fontWeight: '600',
    },
    activeTabIndicator: {
        position: 'absolute',
        bottom: 0,
        left: 0,
        right: 0,
        height: 2,
        backgroundColor: '#00BFFF',
    },
    // Content Styles
    tabContent: {
        flex: 1,
        padding: 16,
        backgroundColor: '#fff',
    },
    // Commission Item Styles
    commissionItem: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: '#fff',
        paddingVertical: 16,
        paddingHorizontal: 16,
        marginVertical: 8,
        borderRadius: 8,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 1,
        },
        shadowOpacity: 0.1,
        shadowRadius: 2,
        elevation: 2,
    },
    commissionLabel: {
        fontSize: 14,
        color: '#333',
        fontWeight: '400',
        flex: 1,
    },
    percentageContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: '#f0f0f0',
        borderRadius: 6,
        paddingVertical: 10,

    },
    percentageInput: {
        fontSize: 11,
        color: '#333',
        fontWeight: '600',
        textAlign: 'left',
        minWidth: 200,
        borderWidth: 0,
        padding: 16,
    },
    // Empty State
    emptyState: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        paddingVertical: 60,
    },
    emptyText: {
        fontSize: 16,
        color: '#999',
        textAlign: 'center',
    },
    // Toggle Switch Styles
    toggleContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'flex-start',
        backgroundColor: '#fff',
        marginVertical: 10,
    },
    toggleLabel: {
        fontSize: 14,
        marginRight: 10,
        color: '#333',
        fontWeight: '400',
    },
    toggleSwitch: {
        width: 50,
        height: 30,
        borderRadius: 15,
        backgroundColor: '#ccc',
        justifyContent: 'center',
        alignItems: 'center',
        padding: 5,
    },
    toggleSwitchActive: {
        backgroundColor: '#1C33FF',
    },
    toggleThumb: {
        width: 20,
        height: 20,
        borderRadius: 10,
        backgroundColor: '#fff',
    },
    toggleThumbActive: {
        transform: [{ translateX: 12 }],
    },
    // Edit Button Styles
    editButtonContainer: {
        alignItems: 'flex-end',
        marginBottom: 16,
    },
    editButton: {
        paddingHorizontal: 12,
        paddingVertical: 6,
        borderRadius: 4,
        minWidth: 50,
        alignItems: 'center',
    },
    editButtonText: {
        color: '#fff',
        fontSize: 12,
        fontWeight: '600',
    },
    actionButtonsContainer: {
        flexDirection: 'row',
        gap: 8,
    },
    saveButton: {
        backgroundColor: '#F5F5F5',
        paddingHorizontal: 8,
        paddingVertical: 6,
        borderRadius: 24,
        minWidth: 30,
        alignItems: 'center',
    },
    saveButtonText: {
        color: '#4CAF50',
        fontSize: 12,
        fontWeight: '600',
    },
    cancelButton: {
        backgroundColor: '#F5F5F5',
        paddingHorizontal: 8,
        paddingVertical: 6,
        borderRadius: 24,
        minWidth: 30,
        alignItems: 'center',
    },
    cancelButtonText: {
        color: '#F44336',
        fontSize: 12,
        fontWeight: '600',
    },
    percentageInputEdit: {
        fontSize: 14,
        color: '#333',
        fontWeight: '600',
        textAlign: 'center',
        minWidth: 150,
        backgroundColor: '#fff',
        borderWidth: 1,
        borderColor: '#E8E8E8',
        borderRadius: 4,
        padding: 16,
    },
    editActionsContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 8,
        marginLeft: 12,
    },

});

export default ConfigAffiliate


