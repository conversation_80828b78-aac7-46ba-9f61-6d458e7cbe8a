/* eslint-disable react-native/no-inline-styles */
import React, { useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
    Animated,
    Dimensions,
    Linking,
    RefreshControl,
    StyleSheet,
    TouchableOpacity,
    View,
    Image,
    Text

} from 'react-native';
import { AppButton, FBottomSheet, showBottomSheet, Winicon } from 'wini-mobile-components';
import { useSelectorCustomerState } from '../../redux/hook/customerHook';
import { User } from '../dto/dto';


const UserInfo = () => {
    const customer = useSelectorCustomerState().data;

    return (
        <View style={styles.userInfo}>
            <Image source={{ uri: customer?.avatarUrl ? customer?.avatarUrl : "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSY8a4_wLmxtZkcsRo6QD_pw3DP4OihLGHekQ&s" }} style={styles.avatar} />
            <View style={styles.userDetails}>
                <Text style={styles.userName}>{customer?.Name}</Text>
                <View style={styles.badge}>
                    <View style={styles.badgeLeft}>
                        <Image source={{
                            uri: "https://static.wikia.nocookie.net/leagueoflegends/images/9/96/Season_2019_-_Gold_1.png/revision/latest/scale-to-width-down/250?cb=20181229234920"

                        }} style={styles.badgeImage} />
                        <Text style={styles.badgeText}>{customer?.rank ? customer?.rank : "Basic"}</Text>
                    </View>
                    <Text style={styles.badgeDot} >
                        .
                    </Text>
                    <Text style={styles.status}>{customer?.Status == 1 ? "Online" : "Locked"}</Text>
                </View>
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    userInfo: {
        flexDirection: 'column',
        alignItems: 'center',
        padding: 15,
        height: "16%",
        margin: "auto"
    },
    avatar: {
        width: 80,
        height: 80,
        borderRadius: 50,
        marginBottom: 10,
    },
    userDetails: {
        flex: 1,
    },
    userName: {
        fontSize: 18,
        fontWeight: 'bold',
        color: '#333',
        alignSelf: "center"
    },
    badge: {
        borderRadius: 10,
        paddingVertical: 2,
        paddingHorizontal: 8,
        marginTop: 5,
        display: "flex",
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "space-evenly"
    },
    badgeLeft: {
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        flexDirection: "row",
        gap: 3
    },

    badgeImage: {
        width: 15,
        height: 15,
    },
    badgeDot: {
        color: "green",
        marginLeft: 3,
        marginRight: 3

    },

    badgeText: {
        color: 'black',
        fontSize: 14
    },
    status: {
        color: "green"
    },

});


export default UserInfo
