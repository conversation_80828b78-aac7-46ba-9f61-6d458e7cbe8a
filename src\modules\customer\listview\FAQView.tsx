import {
  <PERSON><PERSON><PERSON>,
  SafeAreaView,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
  StyleSheet,
  RefreshControl,
  FlatList,
} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import React, {useState, useEffect, useRef, useCallback} from 'react';
import {ColorThemes} from '../../../assets/skin/colors';
import {TypoSkin} from '../../../assets/skin/typography';
import {useForm} from 'react-hook-form';

import {useTranslation} from 'react-i18next';
import RenderHTML from 'react-native-render-html';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import {
  showSnackbar,
  ComponentStatus,
  FPopup,
  TextField,
  Winicon,
  ListTile,
} from 'wini-mobile-components';
import {DataController} from '../../../base/baseController';
import ScreenHeader from '../../../Screen/Layout/header';

// Define FAQ item interface
interface FAQItem {
  Id: string;
  Name: string;
  Content: string;
  CateFAQId?: string;
}
export default function FAQView() {
  const {t} = useTranslation();
  const navigation = useNavigation<any>();
  const [faqCategories, setFaqCategories] = useState<any>([]);
  const [dataFaq, setDataFaq] = useState<{
    data: FAQItem[];
    totalCount: number | undefined;
  }>({
    data: [],
    totalCount: undefined,
  });
  const popupRef = useRef<any>(null);
  const [activeCategory, setActiveCategory] = useState<string | null>(null);

  const [isLoading, setLoading] = useState(false);
  const [isCategoriesLoading, setCategoriesLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [searchValue, setSearchValue] = useState('');
  const filterMethods = useForm({shouldFocusError: false});

  // Handle pull-to-refresh
  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    await getCategories();
    await getData();
    setRefreshing(false);
  }, [activeCategory, searchValue]);

  const getCategories = async () => {
    setCategoriesLoading(true);
    try {
      const _controller = new DataController('CateFAQ');
      const res = await _controller.getAll();

      if (res.code !== 200) {
        showSnackbar({
          message: res.message,
          status: ComponentStatus.ERROR,
        });
        return;
      }

      setFaqCategories(res.data || []);

      // Set the first category as active if there are categories and no active category
      if (res.data && res.data.length > 0 && !activeCategory) {
        setActiveCategory(res.data[0].Id);
      }
    } catch (error) {
      showSnackbar({
        message: 'Lỗi khi tải danh mục',
        status: ComponentStatus.ERROR,
      });
    } finally {
      setCategoriesLoading(false);
    }
  };

  const getData = async () => {
    setLoading(true);
    try {
      const _controller = new DataController('FaqQuestion');
      let searchQuery = [];

      // Filter by active category if one is selected
      if (activeCategory) {
        searchQuery.push(`@CateFAQId:{${activeCategory}}`);
      } else if (filterMethods.getValues('AttributeId')?.length) {
        searchQuery.push(
          `@CateFAQId:{${filterMethods.getValues('AttributeId').join(' | ')}}`,
        );
      }

      // Add search term to query if it exists
      if (searchValue) {
        searchQuery.push(`(@Name:(%${searchValue}%))`);
      }

      const res = await _controller.getPatternList({
        page: 1,
        size: 20,
        query: searchQuery.length ? searchQuery.join(' ') : '*',
        pattern: {
          CateFAQId: ['Id', 'Name'],
        },
      });

      if (res.code !== 200) {
        showSnackbar({
          message: res.message,
          status: ComponentStatus.ERROR,
        });
        return;
      }

      res.data.forEach((e: any) => {
        filterMethods.setValue(`faq${e.Id}`, false);
      });

      setDataFaq({data: res.data, totalCount: res.totalCount});
    } catch (error) {
      showSnackbar({
        message: 'Lỗi khi tải dữ liệu',
        status: ComponentStatus.ERROR,
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // Load categories when component mounts
    getCategories();
  }, []);

  useEffect(() => {
    // Load FAQs when search, filters, or active category changes
    // Only call getData if we have categories loaded
    if (faqCategories.length > 0) {
      getData();
    }
  }, [
    filterMethods.getValues('AttributeId')?.length,
    searchValue,
    activeCategory,
    faqCategories.length, // Add this to ensure we have categories before loading data
  ]);

  // FAQ Item Skeleton Placeholder
  const FAQItemSkeleton = () => {
    return (
      <SkeletonPlaceholder backgroundColor="#f0f0f0" highlightColor="#e0e0e0">
        <View
          style={{
            padding: 16,
            borderBottomColor: ColorThemes.light.Neutral_Border_Color_Main,
            borderBottomWidth: 1,
          }}>
          {/* Title placeholder */}
          <View
            style={{
              width: '80%',
              height: 20,
              borderRadius: 4,
              marginBottom: 8,
            }}
          />

          {/* Icon placeholder */}
          <View
            style={{
              width: 24,
              height: 24,
              borderRadius: 12,
              position: 'absolute',
              right: 16,
              top: 16,
            }}
          />
        </View>
      </SkeletonPlaceholder>
    );
  };

  // Category Tab Skeleton Placeholder
  const CategoryTabSkeleton = () => {
    return (
      <SkeletonPlaceholder backgroundColor="#f0f0f0" highlightColor="#e0e0e0">
        <View
          style={{
            flexDirection: 'row',
            paddingHorizontal: 16,
            paddingVertical: 12,
            gap: 16,
          }}>
          {[1, 2, 3, 4].map(item => (
            <View
              key={item}
              style={{
                width: 100,
                height: 36,
                borderRadius: 18,
              }}
            />
          ))}
        </View>
      </SkeletonPlaceholder>
    );
  };

  const scrollCateRef = useRef<any>(null);

  // Function to scroll to active tab using index-based calculation
  const scrollToActiveTab = useCallback(
    (categoryId: string) => {
      if (scrollCateRef.current && faqCategories.length > 0) {
        const categoryIndex = faqCategories.findIndex(
          (cat: any) => cat.Id === categoryId,
        );
        if (categoryIndex !== -1) {
          console.log('Scrolling to category index:', categoryIndex);

          // More accurate calculation based on actual tab styling
          const tabPadding = 16; // paddingHorizontal from tab style
          const tabGap = 8; // gap from contentContainerStyle
          const containerPadding = 16; // paddingHorizontal from contentContainerStyle

          // Estimate tab width based on text length (more dynamic)
          const category = faqCategories[categoryIndex];
          const estimatedTextWidth = category.Name.length * 8; // rough estimate
          const tabWidth = Math.max(80, estimatedTextWidth + tabPadding * 2);

          const scrollViewWidth = Dimensions.get('window').width;
          const totalWidthBeforeTab =
            containerPadding + categoryIndex * (tabWidth + tabGap);
          const scrollToX = Math.max(
            0,
            totalWidthBeforeTab - scrollViewWidth / 2 + tabWidth / 2,
          );

          console.log('Scroll calculation:', {
            categoryIndex,
            tabWidth,
            scrollViewWidth,
            totalWidthBeforeTab,
            scrollToX,
          });

          setTimeout(() => {
            scrollCateRef.current?.scrollTo({
              x: scrollToX,
              animated: true,
            });
          }, 100);
        }
      }
    },
    [faqCategories],
  );

  // Auto scroll to active category when it changes (only on initial load)
  useEffect(() => {
    if (activeCategory && faqCategories.length > 0 && !isCategoriesLoading) {
      // Only auto-scroll on initial load, not on user interaction
      const isInitialLoad =
        faqCategories.length > 0 && activeCategory === faqCategories[0]?.Id;
      if (isInitialLoad) {
        scrollToActiveTab(activeCategory);
      }
    }
  }, [faqCategories.length, isCategoriesLoading]); // Remove activeCategory from dependencies

  // TabBar component for categories - memoized to prevent unnecessary re-renders
  const CategoryTabBar = React.useMemo(() => {
    if (isCategoriesLoading) {
      return <CategoryTabSkeleton />;
    }

    return (
      <View style={styles.tabBarContainer}>
        <ScrollView
          ref={scrollCateRef}
          horizontal
          showsHorizontalScrollIndicator={false}
          scrollEnabled={true}
          bounces={false}
          onScrollBeginDrag={() => console.log('ScrollView drag started')}
          onScrollEndDrag={() => console.log('ScrollView drag ended')}
          contentContainerStyle={{
            paddingHorizontal: 16,
            paddingVertical: 8,
            gap: 8,
          }}>
          {faqCategories.map((category: any) => (
            <TouchableOpacity
              key={category.Id}
              activeOpacity={0.7}
              hitSlop={{top: 10, bottom: 10, left: 5, right: 5}}
              onPress={() => {
                console.log('Tab pressed:', category.Name);
                // Only update if it's a different category
                if (activeCategory !== category.Id) {
                  setActiveCategory(category.Id);
                  // Scroll after a small delay to ensure state update
                  setTimeout(() => {
                    scrollToActiveTab(category.Id);
                  }, 50);
                }
              }}
              style={[
                styles.tab,
                activeCategory === category.Id && styles.activeTab,
              ]}>
              <Text
                style={[
                  styles.tabText,
                  activeCategory === category.Id && styles.activeTabText,
                ]}>
                {category.Name}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>
    );
  }, [faqCategories, activeCategory, isCategoriesLoading, scrollToActiveTab]);

  return (
    <SafeAreaView style={styles.container}>
      <FPopup ref={popupRef} />
      <ScreenHeader
        onBack={() => {
          navigation.pop();
        }}
        title={t('profile.faq')}
        bottom={
          <View style={styles.searchContainer}>
            <TextField
              style={styles.searchField}
              onChange={vl => {
                setSearchValue(vl.trim());
              }}
              placeholder={t('common.search')}
              prefix={
                <Winicon
                  src="outline/development/zoom"
                  size={14}
                  color={ColorThemes.light.Neutral_Text_Color_Subtitle}
                />
              }
            />
          </View>
        }
      />
      {/* Category TabBar */}
      {CategoryTabBar}

      {/* FAQ List */}
      {isLoading ? (
        // Show skeleton placeholders when loading
        <View style={styles.faqListContainer}>
          <FAQItemSkeleton />
          <FAQItemSkeleton />
          <FAQItemSkeleton />
          <FAQItemSkeleton />
        </View>
      ) : (
        <FlatList<FAQItem>
          style={styles.faqListContainer}
          data={dataFaq.data}
          keyExtractor={(item, index) => `faq-${item.Id || index}`}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[ColorThemes.light.Primary_Color_Main]}
              tintColor={ColorThemes.light.Primary_Color_Main}
            />
          }
          ListEmptyComponent={
            <View style={styles.emptyContainer}>
              <Text style={styles.emptyText}>Không có câu hỏi nào</Text>
            </View>
          }
          renderItem={({item, index}) => (
            <ListTile
              key={index}
              style={styles.faqItem}
              title={`${item?.Name}`}
              onPress={() => {
                filterMethods.setValue(
                  `faq${item.Id}`,
                  !filterMethods.getValues(`faq${item.Id}`),
                );
              }}
              trailing={
                <Winicon
                  src={
                    filterMethods.watch(`faq${item.Id}`)
                      ? 'outline/arrows/arrow-sm-down'
                      : 'fill/arrows/arrow-sm-right'
                  }
                  size={24}
                  color={ColorThemes.light.Neutral_Text_Color_Body}
                />
              }
              bottom={
                filterMethods.watch(`faq${item.Id}`) ? (
                  <View style={styles.faqContent}>
                    <Text
                      style={{
                        ...TypoSkin.subtitle3,
                        color: ColorThemes.light.Neutral_Text_Color_Body,
                      }}>
                      {item?.Content.includes('<') ? (
                        <RenderHTML
                          contentWidth={Dimensions.get('window').width}
                          source={{html: item?.Content}}
                        />
                      ) : (
                        item?.Content ?? ''
                      )}
                    </Text>
                  </View>
                ) : null
              }
            />
          )}
        />
      )}
    </SafeAreaView>
  );
}

// Add styles for the component
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
  },
  tabBar: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  tabBarContainer: {
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
    borderBottomWidth: 1,
    borderBottomColor: ColorThemes.light.Neutral_Border_Color_Main,
  },
  tab: {
    height: 36,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 16,
    borderRadius: 18,
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Main,
  },
  activeTab: {
    backgroundColor: ColorThemes.light.Primary_Color_Main,
  },
  tabText: {
    ...TypoSkin.buttonText5,
    color: ColorThemes.light.Neutral_Text_Color_Body,
  },
  activeTabText: {
    color: ColorThemes.light.Neutral_Background_Color_Absolute,
  },
  searchContainer: {
    flexDirection: 'row',
    width: '100%',
    paddingHorizontal: 16,
    gap: 8,
    paddingBottom: 16,
  },
  searchField: {
    paddingHorizontal: 16,
    flex: 1,
    height: 40,
    textAlign: 'left',
    paddingVertical: 0,
  },
  faqListContainer: {
    flex: 1,
    marginTop: 0, // Loại bỏ khoảng trắng giữa tab và danh sách
    paddingTop: 0,
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
  },
  faqItem: {
    borderBottomColor: ColorThemes.light.Neutral_Border_Color_Main,
    borderBottomWidth: 1,
  },
  faqContent: {
    paddingTop: 8,
    alignItems: 'flex-start',
    width: '100%',
  },
  emptyContainer: {
    padding: 16,
    alignItems: 'center',
  },
  emptyText: {
    ...TypoSkin.subtitle2,
    color: ColorThemes.light.Neutral_Text_Color_Subtitle,
  },
});
