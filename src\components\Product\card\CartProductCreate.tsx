import React, { use, useEffect, useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Pressable, Image, ScrollView } from 'react-native';
import { ListTile, Winicon } from 'wini-mobile-components';
import ConfigAPI from '../../../Config/ConfigAPI';


const ListItemCard = ({ item, index }: { item: any; index: number }, isSelected: string, handleSelect: any) => {
    {
        return (
            <Pressable
                style={styles.itemContainer}
            >
                <View style={styles.itemText}>
                    <Image
                        source={{
                            uri: ConfigAPI.urlImg + item.Img
                        }}
                        style={{ width: 30, height: 30, borderRadius: 100 }}
                    />
                    <Text style={{ marginLeft: 10, fontSize: 20 }}>
                        {item.Name}
                    </Text>
                </View>
                <TouchableOpacity style={isSelected == item.Id ? [styles.radio, isSelected && styles.radioSelected] : styles.radio} onPress={() => handleSelect(item)} />
            </Pressable>
        );
    };
}

const styles = StyleSheet.create({
    itemContainer: {
        flexDirection: 'row',
        justifyContent: 'space-around',
        alignItems: 'center',
        padding: 10,
        borderBottomWidth: 1,
        borderBottomColor: '#eee',
    },
    itemText: {
        fontSize: 20,
        color: 'black',
        flex: 1,
        marginLeft: 20,
        flexDirection: "row",
        alignItems: "center",
        padding: 10,
    },
    radio: {
        width: 30,
        height: 30,
        borderRadius: 50,
        borderWidth: 2,
        borderColor: '#007AFF',
    },
    radioSelected: {
        backgroundColor: '#007AFF',
        padding: 5
    },
});

export default ListItemCard;