/* eslint-disable react-native/no-inline-styles */
import { useNavigation } from '@react-navigation/native';
import React, { useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
    Animated,
    Dimensions,
    Linking,
    RefreshControl,
    StyleSheet,
    TouchableOpacity,
    View,
    Image,
    Text

} from 'react-native';
import { AppButton, FBottomSheet, showBottomSheet, Winicon } from 'wini-mobile-components';
import { NumberStatusIcon } from '../../Config/Contanst';
import { shopData } from '../../mock/shopData';

interface NavigateShopProps {
    title: string
    status?: number
}

const NavigateShop = (props: NavigateShopProps) => {
    let { title, status } = props
    const navigation = useNavigation<any>();
    useEffect(() => {
        console.log("shopData", shopData)
    }, [])

    const handleBack = () => {
        navigation.goBack()
    }

    return (
        <View style={styles.navigator}>
            <TouchableOpacity style={styles.back} onPress={() => handleBack()} >
                <View style={{ flexDirection: "row", alignItems: "center" }}>
                    <Winicon src="outline/arrows/left-arrow" size={14} color={'black'} style={styles.navigatorIcon} />
                </View>
                <View style={{ flexDirection: "row", alignItems: "center" }}>
                    <Image
                        source={require('../../assets/images/logo.png')}
                        style={styles.navigatorImage}
                    />
                </View>
            </TouchableOpacity>
            <Text style={styles.titleNavigator}>{title}</Text>
            {status && status == NumberStatusIcon.One ?
                <View style={styles.noti}>
                    <View style={styles.navigatorImage} >
                    </View>
                    <View style={styles.navigatorImage}>
                    </View>
                </View>
                :
                <View style={styles.noti}>
                    <TouchableOpacity >
                        <Image
                            source={require('../../assets/images/icon_navigator.png')}
                            style={styles.navigatorImage}
                        />
                    </TouchableOpacity>
                    <TouchableOpacity >
                        <Image
                            source={require('../../assets/images/icon_bell_navigator.png')}
                            style={styles.navigatorImage}
                        />
                    </TouchableOpacity>
                </View>

            }


        </View>
    );
}

const styles = StyleSheet.create({
    navigator: {
        display: "flex",
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "space-between",
        borderBottomColor: "#00FFFF",
        paddingBottom: 18,
        borderBottomWidth: 0.5,
        width: "100%"
    },
    navigatorImage: {
        width: 32,
        height: 32,
        borderRadius: 50
    },
    navigatorIcon: {
        marginTop: 4
    },
    titleNavigator: {
        fontSize: 26,
        fontFamily: "roboto",
        marginLeft: 20
    },
    back: {
        display: "flex",
        flexDirection: "row",
        marginLeft: 15

    },
    noti: {
        display: "flex",
        flexDirection: "row",
        marginRight: 15,
        gap: 2,
    },


});


export default NavigateShop
