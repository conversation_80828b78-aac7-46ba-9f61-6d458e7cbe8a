import {useNavigation, useRoute} from '@react-navigation/native';
import React, {useEffect, useState} from 'react';
import {Controller, useForm} from 'react-hook-form';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  KeyboardAvoidingView,
} from 'react-native';
import {ScrollView} from 'react-native-gesture-handler';
import {Winicon, ComponentStatus, showSnackbar} from 'wini-mobile-components';
import {TextFieldForm} from '../../modules/news/form/component-form';
import {ColorThemes} from '../../assets/skin/colors';
import {black} from 'react-native-paper/lib/typescript/styles/themes/v2/colors';
import {validatePhoneNumber} from '../../utils/validate';
import {
  DropdownForm,
  FAddressPickerForm,
} from '../../modules/Default/form/component-form';
import {shopData} from '../../mock/shopData';
import {StoreInfoFormProps} from '../dto/dto';
import {DataController} from '../../base/baseController';
import {randomGID} from '../../utils/Utils';
import {useSelectorCustomerState} from '../../redux/hook/customerHook';
import {RootScreen} from '../../router/router';
import {TypeMenuShop} from '../../Config/Contanst';
import {useDispatch} from 'react-redux';
import {ProductActions} from '../../redux/reducers/ShoptReducer';

const StoreInfoForm = (props: StoreInfoFormProps) => {
  const {statusInput, data} = props;
  const methods = useForm({shouldFocusError: false});
  const navigation = useNavigation<any>();
  const customer = useSelectorCustomerState().data;
  const dispatch = useDispatch<any>();

  const onSubmit = async (data: any) => {
    let res;
    if (data.Phone) {
      if (!/^(\+84|0)/.test(data.Phone)) {
        data.Phone = '0' + data.Phone; // Add 0 at the beginning
      }
      const val = validatePhoneNumber(data.Phone);
      if (val) methods.clearErrors('Phone');
      else {
        methods.setError('Phone', {
          message: 'Số điện thoại không hợp lệ',
        });
        return;
      }
    }

    if (data.Email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      var email = data?.Email.trim();
      let checkMail = emailRegex.test(email);
      if (checkMail) methods.clearErrors('Email');
      else {
        methods.setError('Email', {
          message: 'Email không hợp lệ',
        });
        return;
      }
    }
    const newShop = {
      Id: statusInput == 'edit' ? data.Id : randomGID(),
      Name: methods.watch('StoreName'),
      DateCreated: statusInput == 'edit' ? data.DateCreated : Date.now(),
      Mobile: methods.watch('Phone'),
      Status: methods.watch('Status'),
      Description: methods.watch('Description'),
      Address: data.Address,
      Email: data.Email,
      CustomerId: customer.Id,
    };
    if (statusInput == 'edit') {
      dispatch(ProductActions.editShop([newShop])).then((res: any) => {
        if (res.code == 200) {
          showSnackbar({
            message: 'Chỉnh sửa shop thành công',
            status: ComponentStatus.SUCCSESS,
          });
          dispatch(ProductActions.getInforShop(customer.Id));
          navigation.goBack();
        }
      });
    } else {
      dispatch(ProductActions.addShop([newShop])).then((res: any) => {
        if (res.code == 200) {
          showSnackbar({
            message: 'Đăng ký shop thành công',
            status: ComponentStatus.SUCCSESS,
          });
          // quay lại trang trước và cập nhật lại dữ liệu
          dispatch(ProductActions.getInforShop(customer.Id));
          navigation.goBack();
        }
      });
    }
  };

  useEffect(() => {
    methods.setValue('Id', data?.Id);
    methods.setValue('DateCreated', data?.DateCreated);
    methods.setValue('StoreName', data?.name);
    methods.setValue('Phone', data?.Phone);
    methods.setValue('Email', data?.Email);
    // methods.setValue("BusinessType", data?.BusinessType);
    methods.setValue('Description', data?.description);
    methods.setValue('Status', data?.Status);
    methods.setValue('Address', data?.Address);
  }, [data]);

  let textFieldStyle = {
    height: 48,
    paddingLeft: 8,
    backgroundColor: ColorThemes.light.transparent,
    borderWidth: 0,
    // borderBottomWidth: 0.8,
    // borderBlockColor: ColorThemes.light.primary_main_color,
  };

  const prefix = (icon: string) => {
    return (
      <View
        style={{
          flexDirection: 'row',
          height: 32,
          width: 32,
          alignItems: 'center',
          justifyContent: 'center',
        }}>
        <Winicon src={icon} size={24} color="black" />
      </View>
    );
  };

  return (
    <KeyboardAvoidingView behavior="padding" enabled>
      <ScrollView style={styles.container}>
        <KeyboardAvoidingView style={{width: '100%', gap: 24}}>
          <TextFieldForm
            control={methods.control}
            name="StoreName"
            placeholder="Nhập tên cửa hàng của bạn"
            returnKeyType="done"
            errors={methods.formState.errors}
            textFieldStyle={textFieldStyle}
            register={methods.register}
            required
            prefix={prefix('outline/user interface/home')}
          />

          <TextFieldForm
            control={methods.control}
            name="Phone"
            placeholder="Nhập Số điện thoại cửa hàng"
            returnKeyType="done"
            errors={methods.formState.errors}
            textFieldStyle={textFieldStyle}
            register={methods.register}
            type="phone-pad"
            required
            prefix={prefix('outline/user interface/mirror-tablet-phone')}
          />
          <TextFieldForm
            control={methods.control}
            name="Email"
            placeholder="Email"
            returnKeyType="done"
            errors={methods.formState.errors}
            textFieldStyle={textFieldStyle}
            register={methods.register}
            required
            prefix={prefix('outline/user interface/email')}
          />
          {/* 
					<TextFieldForm
						control={methods.control}
						name="BusinessType"
						placeholder="Lĩnh vực kinh doanh"
						returnKeyType="done"
						errors={methods.formState.errors}
						textFieldStyle={textFieldStyle}
						register={methods.register}
						required
						prefix={
							prefix("fill/business/office-badge")
						}
					/> */}
          <TextFieldForm
            control={methods.control}
            name="Description"
            placeholder="Giới thiệu ngắn"
            returnKeyType="done"
            errors={methods.formState.errors}
            textFieldStyle={textFieldStyle}
            register={methods.register}
            required
            prefix={prefix('fill/multimedia/audio-description')}
          />
          <View style={styles.selectFieldStyle}>
            <Winicon
              src="outline/user interface/gear"
              size={24}
              color="#262626"
            />
            <DropdownForm
              control={methods.control}
              errors={methods.formState.errors}
              style={{
                flex: 1,
                marginLeft: 10,
                marginRight: 15,
              }}
              required
              placeholder="Chọn trạng thái"
              name="Status"
              options={[
                {id: 0, name: 'Khởi tạo'},
                {id: 1, name: 'Đang hoạt động'},
              ]}
            />
          </View>

          {!statusInput && (
            <FAddressPickerForm
              control={methods.control}
              errors={methods.formState.errors}
              name="Address"
              prefix={
                <View
                  style={{
                    flexDirection: 'row',
                    height: 32,
                    width: 32,
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}>
                  <Winicon
                    src="fill/user interface/password"
                    size={24}
                    color="black"
                  />
                </View>
              }
              placeholder="Nhập địa chỉ của bạn"
              onChange={value => {
                methods.setValue('Long', value.geometry.location.lng);
                methods.setValue('Lat', value.geometry.location.lat);
                methods.setValue('Address', value.formatted_address);
                return value.formatted_address;
              }}
              textFieldStyle={{
                paddingLeft: 8,
                gap: 12,
                height: 32,
                borderWidth: 0,
              }}
            />
          )}

          <TouchableOpacity
            style={styles.buyButton}
            onPress={methods.handleSubmit(onSubmit)}>
            <Text style={styles.actionButtonText}>Hoàn Thành</Text>
          </TouchableOpacity>
        </KeyboardAvoidingView>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
    backgroundColor: '#fff',
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#d3e3fd',
    paddingVertical: 8,
    gap: 4,
    marginBottom: 8,
  },
  icon: {
    marginRight: 8,
  },
  input: {
    flex: 1,
    fontSize: 16,
    color: 'black',
  },
  addressText: {
    flex: 1,
    fontSize: 16,
    color: '#999',
    marginLeft: 3,
  },
  mapIcon: {
    marginLeft: 8,
  },
  button: {
    backgroundColor: '#2196F3',
    borderRadius: 25,
    paddingVertical: 15,
    marginHorizontal: 20,
    marginBottom: 30,
    alignItems: 'center',
  },
  buttonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  buyButton: {
    backgroundColor: 'blue',
    width: '80%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 50,
    borderRadius: 30,
    margin: 'auto',
    marginTop: 20,
  },

  actionButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
  },
  inputError: {
    borderColor: 'red',
  },
  errorText: {
    color: 'red',
  },
  selectFieldStyle: {
    height: 48,
    paddingLeft: 8,
    marginLeft: 5,
    gap: 5,
    flexDirection: 'row',
    alignItems: 'center',
  },
});

export default StoreInfoForm;
