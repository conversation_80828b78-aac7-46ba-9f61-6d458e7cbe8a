import React, { useEffect, useState } from 'react';
import { View, Text, Image, TouchableOpacity, StyleSheet, Pressable } from 'react-native';
import { FlatList, ScrollView } from 'react-native-gesture-handler';
import { FLoading, Winicon } from 'wini-mobile-components';
import { Title } from '../../../Config/Contanst';
import { OrderData } from '../../../mock/shopData';
import ConfigAPI from '../../../Config/ConfigAPI';

const CardOrder = ({ item, index }: { item: any; index: number }, action: string, loading: boolean, handleUpdateStatusProcessOrder: (item: any, type?: string) => void, handleUpdateStatusProcessOrderCancel: (item: any) => void) => {
    return (
        <Pressable key={`key ${index}`}>
            <FLoading visible={loading} />
            <View style={styles.container}>
                <View style={styles.header}>
                    <Text style={styles.orderId}><PERSON><PERSON><PERSON> hàng {item.Id}</Text>
                    <Text style={styles.status}>
                        {item.Status == 1 && "Đang thực hiện"}
                        {item.Status == 2 && "<PERSON><PERSON> thực hiện"}
                        {item.Status == 3 && "Hoàn thành"}
                        {item.Status == 4 && "Hủy"}
                    </Text>
                </View>
                {/* Thông tin sản phẩm */}
                <View style={styles.productContainer}>
                    <Image
                        source={{ uri: ConfigAPI.urlImg + item.Img }}
                        style={styles.productImage}
                    />
                    <View style={styles.productInfo}>
                        <Text style={styles.productName}>{item.Name}</Text>
                        <Text style={styles.productDetails}>
                            {item.Description}
                        </Text>
                        <Text >
                            {item?.Property}
                        </Text>
                        <Text style={styles.productPrice}>Giá: {item.Value} VNĐ</Text>
                    </View>
                </View>

                {/* Số lượng và tổng tiền */}
                <View style={styles.quantityTotal}>
                    <TouchableOpacity style={styles.quantityButton}>
                        <Text style={styles.quantityText}>
                            <Text>Xem thêm</Text>
                            <Winicon src="color/arrows/arrow-down" size={13} color="#999" />
                        </Text>
                    </TouchableOpacity>
                    <View style={styles.quantityDetail}>
                        <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'center' }}>
                            <Text style={styles.quantity} >Tổng tiền ({item.NumberQuality ? item.NumberQuality : 0} sản phẩm):</Text>
                            <Text style={styles.money} > {Number(item.Value * item.NumberQuality)} VNĐ</Text>
                        </View>

                    </View>

                </View>
                {
                    action !== Title.Cancel &&
                    <View style={styles.button}>
                        <View style={{ flexDirection: "row", gap: 10 }}>

                        </View>
                        <View style={{ flexDirection: "row", gap: 10 }}>
                            <TouchableOpacity style={styles.confirmButton} onPress={() => handleUpdateStatusProcessOrder(item)}>
                                <Text style={styles.confirmButtonText} >{action ? action : ""}</Text>
                            </TouchableOpacity>
                            {item?.Status == 2 &&
                                <TouchableOpacity style={styles.confirmButton} onPress={() => handleUpdateStatusProcessOrderCancel(item)} >
                                    <Text style={styles.confirmButtonText} >Xác nhận hủy</Text>
                                </TouchableOpacity>
                            }
                            {item?.Status == 3 &&
                                <TouchableOpacity style={styles.confirmButton} >
                                    <Text style={styles.confirmButtonText} >Yêu cầu hoàn hàng</Text>
                                </TouchableOpacity>
                            }
                        </View>

                    </View>
                }

            </View >
        </Pressable >


    );
};

const styles = StyleSheet.create({
    container: {
        backgroundColor: '#fff',
        borderRadius: 8,
        borderWidth: 2,
        borderColor: '#ddd',
        padding: 10,
        margin: 10,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 2, // Bóng cho Android
        marginTop: 10
    },
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: 10,
    },
    orderId: {
        fontSize: 14,
        fontWeight: 'bold',
        color: '#333',
    },
    status: {
        fontSize: 13,
        color: '#FF0000', // Màu đỏ cho trạng thái
    },
    productContainer: {
        flexDirection: 'row',
        marginBottom: 10,
    },
    productImage: {
        width: 50,
        height: 50,
        borderRadius: 8,
        marginRight: 10,
    },
    productInfo: {
        flex: 1,
    },
    productName: {
        fontSize: 14,
        fontWeight: 'bold',
        color: '#333',
    },
    productDetails: {
        fontSize: 12,
        color: '#666',
        marginVertical: 2,
    },
    productPrice: {
        fontSize: 12,
        color: '#333',
    },
    quantityTotal: {
        flexDirection: 'column',
        justifyContent: 'space-evenly',
        alignItems: 'center',
        marginBottom: 10,
    },
    quantityButton: {
        borderRadius: 5,
        paddingHorizontal: 10,
        paddingVertical: 5,
    },
    quantityText: {
        fontSize: 14,
        color: '#555',
        display: "flex",
        gap: 1
    },
    quantityDetail: {
        height: 30,
        width: '100%',
        alignItems: "flex-end"
    },
    quantity: {
        fontSize: 14,
        fontWeight: 'bold',
        color: '#333',
        display: "flex",
        justifyContent: "flex-end"
    },
    money: {
        fontSize: 16,
        color: "red",

    },
    button: {
        minHeight: 40,
        width: "100%",
        display: "flex",
        alignItems: "flex-end"
    },
    confirmButton: {
        backgroundColor: '#FFA500',
        width: 150,
        borderRadius: 50,
        paddingVertical: 10,
        alignItems: 'center',
    },
    confirmButtonText: {
        fontSize: 16,
        color: 'red',
        fontWeight: 'bold',
    },
});

export default CardOrder;