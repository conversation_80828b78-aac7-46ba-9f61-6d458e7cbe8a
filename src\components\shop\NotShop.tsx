/* eslint-disable react-native/no-inline-styles */
import React, { useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
    Animated,
    Dimensions,
    Linking,
    RefreshControl,
    StyleSheet,
    TouchableOpacity,
    View,
    Image,
    Text

} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { RootScreen } from '../../router/router';


const NotShop = () => {
    const navigation = useNavigation<any>();

    const handleRegisterShop = () => {
        navigation.navigate(RootScreen.RegisterShop,);
    }
    return (
        <View style={styles.content}>
            <Image
                source={require('../../assets/images/registerShop.png')}
                style={styles.illustration}
            />
            <Text style={styles.description}>
                Bạn là khách hàng cá nhân, Chọn{'\n'}
                <Text style={styles.boldText}>“<PERSON><PERSON>ng ký”</Text> để chuyển đổi thành cửa hàng
            </Text>
            <TouchableOpacity style={styles.buyButton} onPress={() => handleRegisterShop()}>
                <Text style={styles.actionButtonText} >Đ<PERSON>ng <PERSON>ý</Text>
            </TouchableOpacity>
        </View>
    );
};

const styles = StyleSheet.create({
    content: {
        flex: 1,
        alignItems: 'center',
    },
    illustration: {
        width: 231,
        height: 203.54,
        marginBottom: 20,
    },
    description: {
        fontSize: 16,
        color: '#666',
        textAlign: 'center',
        marginTop: 50,
        fontFamily: "Roboto"
    },
    boldText: {
        fontWeight: 'bold',
        color: '#333',
    },
    button: {
        backgroundColor: '#2196F3',
        borderRadius: 25,
        paddingVertical: 15,
        marginHorizontal: 20,
        marginBottom: 30,
        alignItems: 'center',
    },
    buttonText: {
        color: '#fff',
        fontSize: 18,
        fontWeight: 'bold',
    },
    buyButton: {
        backgroundColor: 'blue',
        width: 325,
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        marginTop: 25,
        minHeight: 58,
        borderRadius: 30
    },

    actionButtonText: {
        color: '#fff',
        fontSize: 17,
        fontWeight: '600',
        textAlign: 'center',
    },

});


export default NotShop
