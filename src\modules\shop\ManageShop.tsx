/* eslint-disable react-native/no-inline-styles */
import React, {use, useEffect, useRef, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {
  Animated,
  Dimensions,
  Linking,
  RefreshControl,
  StyleSheet,
  TouchableOpacity,
  View,
  Image,
  Text,
} from 'react-native';
import {
  AppButton,
  FBottomSheet,
  FDialog,
  FLoading,
  showBottomSheet,
  Winicon,
} from 'wini-mobile-components';
import {useNavigation, useRoute} from '@react-navigation/native';
import {navigate, RootScreen} from '../../router/router';
import {ColorThemes} from '../../assets/skin/colors';
import {TypoSkin} from '../../assets/skin/typography';
import {ScrollView} from 'react-native-gesture-handler';
import {DataController} from '../../base/baseController';
import {SwiperFlatList} from 'react-native-swiper-flatlist';
import ConfigAPI from '../../Config/ConfigAPI';
import FastImage from 'react-native-fast-image';
import CategoryGrid from '../../modules/category/CategoryGrid';
import {useSelectorCustomerState} from '../../redux/hook/customerHook';
import ProductBestSeller from '../../modules/Product/productBestSeller';
import ByNewTrending from '../../modules/news/listview/byTrending';
import HotProductsSection from '../../modules/Product/HotProductsSection';
import HeaderGroup from '../../components/HeaderGroup';
import Svg, {Path} from 'react-native-svg';
import HeaderShop from '../../components/shop/HeaderShop';
import NavigateShop from '../../components/shop/NavigateShop';
import UserInfo from '../../components/shop/UserInfo';
import NotShop from '../../components/shop/NotShop';
import HaveShop from '../../components/shop/HaveShop';
import {NumberStatusIcon, Title, TypeMenuShop} from '../../Config/Contanst';
import LinearGradient from 'react-native-linear-gradient';
import MenuShop from '../../components/shop/MenuShop';
import {OrderData, shopData} from '../../mock/shopData';
import {ShopDA} from './da';
import {dialogCheckAcc} from '../../Screen/Layout/mainLayout';
import {useSelectorShopState} from '../../redux/hook/shopHook ';
import {useDispatch} from 'react-redux';
import {OrderActions} from '../../redux/reducers/OrderReducer';
import {useSelectorOrderState} from '../../redux/hook/orderHook ';
import {ProductActions} from '../../redux/reducers/ProductReducer';

const Shop = () => {
  const route = useRoute<any>();
  const [select, setSelect] = useState(
    route?.params?.select ? route?.params?.select : TypeMenuShop.User,
  );
  const shopInfo = useSelectorShopState().data;
  const [shop, setShop] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const dialogRef = useRef<any>(null);
  const dispatch = useDispatch<any>();

  useEffect(() => {
    if (select == TypeMenuShop.Shop && shopInfo) {
      dispatch(ProductActions.getInforProduct(shopInfo[0]?.Id));
      setShop(shopInfo);
    }
  }, [select, shopInfo]);
  return (
    <View style={styles.container}>
      <FDialog ref={dialogRef} />
      <FLoading visible={loading} />
      <View style={styles.header}>
        <HeaderShop />
      </View>
      <NavigateShop title={Title.Shop} />
      <UserInfo />
      <MenuShop select={select} setSelect={setSelect} />
      {select == TypeMenuShop.Shop && shop && shop.length > 0 ? (
        <HaveShop shop={shop} />
      ) : (
        <NotShop />
      )}
    </View>
  );
};
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    maxHeight: 120,
  },

  navigator: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderBottomColor: '#00FFFF',
    paddingBottom: 18,
    borderBottomWidth: 0.5,
  },

  icon: {
    width: 24,
    height: 24,
  },
});

export default Shop;
